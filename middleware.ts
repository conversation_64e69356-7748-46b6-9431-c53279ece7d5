import createMiddleware from 'next-intl/middleware';

const middleware = createMiddleware({
  // A list of all locales that are supported
  locales: ['de', 'pl', 'en'],

  // Used when no locale matches
  defaultLocale: 'de',

  // Always use a locale prefix
  localePrefix: 'always'
});

export default function(request: any) {
  console.log('🔍 Middleware called for:', request.nextUrl.pathname);
  return middleware(request);
}

export const config = {
  // Match only internationalized pathnames
  matcher: [
    // Enable a redirect to a matching locale at the root
    '/',
    // Match all pathnames except for
    // - … if they start with `/api`, `/_next` or `/_vercel`
    // - … the ones containing a dot (e.g. `favicon.ico`)
    '/((?!api|_next|_vercel|.*\\..*).*)'
  ]
};
